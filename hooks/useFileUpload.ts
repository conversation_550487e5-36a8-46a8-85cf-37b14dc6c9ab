import { useState } from 'react'
import { validateFileWithCategory } from '@/lib/storage/validation'
import { STORAGE_ERRORS } from '@/lib/storage/config'
import { ATTACHMENT_LIMITS, ATTACHMENT_API_PATHS } from '@/lib/config/attachments'
import { FILE_CATEGORIES, type FileCategory } from '@/lib/config/fileTypes'

// Types
export interface FileWithPreview {
  file: File
  previewUrl: string
  id: string
}

export interface FileUploadResult {
  success: boolean
  error?: string
}

export interface UseMultipleFileUploadResult {
  selectedFiles: FileWithPreview[]
  uploadingFiles: boolean
  addFile: (file: File) => FileUploadResult
  removeFile: (id: string) => void
  uploadAll: () => Promise<string[]>
  canAddMore: boolean
}

// Utility functions
function generateUniqueId(): string {
  return `${Date.now()}-${Math.random().toString(36).substring(2, 8)}`
}

function createFileWithPreview(file: File): FileWithPreview {
  return {
    file,
    previewUrl: URL.createObjectURL(file),
    id: generateUniqueId()
  }
}

export function useMultipleFileUpload(
  maxFiles: number = ATTACHMENT_LIMITS.MAX_PER_NOTE,
  category: FileCategory = FILE_CATEGORIES.IMAGE
): UseMultipleFileUploadResult {
  const [selectedFiles, setSelectedFiles] = useState<FileWithPreview[]>([])
  const [uploadingFiles, setUploadingFiles] = useState(false)

  const addFile = (file: File): FileUploadResult => {
    // Check if we can add more files
    if (selectedFiles.length >= maxFiles) {
      return { success: false, error: `Максимум ${maxFiles} файлов разрешено` }
    }

    // Validate file using centralized validation
    const validation = validateFileWithCategory({
      name: file.name,
      size: file.size,
      type: file.type
    }, category)

    if (!validation.isValid) {
      return { success: false, error: validation.error }
    }

    // Create file with preview
    const fileWithPreview = createFileWithPreview(file)
    setSelectedFiles(prev => [...prev, fileWithPreview])

    return { success: true }
  }

  const removeFile = (id: string) => {
    setSelectedFiles(prev => {
      const fileToRemove = prev.find(f => f.id === id)
      if (fileToRemove) {
        URL.revokeObjectURL(fileToRemove.previewUrl)
      }
      return prev.filter(f => f.id !== id)
    })
  }

  const uploadAll = async (): Promise<string[]> => {
    if (selectedFiles.length === 0) {
      return []
    }

    try {
      setUploadingFiles(true)

      const uploadUrls = await getUploadUrls()
      const publicUrls = await uploadFilesToStorage(uploadUrls)

      return publicUrls
    } catch (error) {
      console.error('Files upload error:', error)
      throw error
    } finally {
      setUploadingFiles(false)
    }
  }

  // Helper function to get upload URLs from API
  const getUploadUrls = async () => {
    const filesInfo = selectedFiles.map(({ file }) => ({
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      category: category // Add category to each file
    }))

    const response = await fetch(ATTACHMENT_API_PATHS.UPLOAD_URL, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ files: filesInfo })
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || STORAGE_ERRORS.UPLOAD_URL_FAILED)
    }

    const { uploads } = await response.json()
    return uploads
  }

  // Helper function to upload files to storage
  const uploadFilesToStorage = async (uploads: Array<{ uploadUrl: string; publicUrl: string }>) => {
    const uploadPromises = selectedFiles.map(async ({ file }, index) => {
      const { uploadUrl, publicUrl } = uploads[index]

      const response = await fetch(uploadUrl, {
        method: 'PUT',
        body: file,
        headers: { 'Content-Type': file.type }
      })

      if (!response.ok) {
        throw new Error(`${STORAGE_ERRORS.UPLOAD_FAILED}: ${file.name}`)
      }

      return publicUrl
    })

    return Promise.all(uploadPromises)
  }

  const canAddMore = selectedFiles.length < maxFiles

  return {
    selectedFiles,
    uploadingFiles,
    addFile,
    removeFile,
    uploadAll,
    canAddMore
  }
}


