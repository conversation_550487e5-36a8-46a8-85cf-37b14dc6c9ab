import { useState, useEffect } from 'react'

export function useTags() {
  const [tags, setTags] = useState<string[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const loadTags = async () => {
    try {
      setError(null)
      const response = await fetch('/api/tags', {
        // Add cache control to prevent stale data
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache'
        }
      })
      if (response.ok) {
        const data = await response.json()
        setTags(data.tags)
      } else {
        console.error('[useTags] Failed to load tags, status:', response.status)
        setError('Не удалось загрузить теги')
      }
    } catch (err) {
      console.error('[useTags] Error loading tags:', err)
      setError('Ошибка при загрузке тегов')
    }
  }

  useEffect(() => {
    loadTags().finally(() => setLoading(false))
  }, [])

  return {
    tags,
    loading,
    error,
    refetch: loadTags
  }
}
