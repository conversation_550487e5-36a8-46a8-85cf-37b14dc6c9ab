"use client"

import { useState, useEffect, useCallback } from "react"
import { FileText, Sparkles, X, AlertCircle } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { createClient } from '@/lib/supabase/client'
import { useRouter } from 'next/navigation'
import { useNotes } from '@/hooks/useNotes'
import { useTags } from '@/hooks/useTags'
import { useAISearch } from '@/hooks/useAISearch'
import { SearchAndFilter } from '@/components/notes/SearchAndFilter'
import { NoteComposer } from '@/components/notes/NoteComposer'
import { NotesList } from '@/components/notes/NotesList'
import { DeleteNoteDialog } from '@/components/notes/DeleteNoteDialog'
import { EditNoteDialog } from '@/components/notes/EditNoteDialog'
import { ThemeToggle } from '@/components/theme-toggle'
import ImageLightbox from '@/components/notes/ImageLightbox'
import { Note } from '@/types/notes'

// Custom hook for lightbox state management
function useLightbox() {
  const [lightboxOpen, setLightboxOpen] = useState(false)
  const [lightboxImages, setLightboxImages] = useState<string[]>([])
  const [lightboxIndex, setLightboxIndex] = useState(0)

  const handleOpenLightbox = useCallback((images: string[], startIndex: number) => {
    setLightboxImages(images)
    setLightboxIndex(startIndex)
    setLightboxOpen(true)
  }, [])

  const handleCloseLightbox = useCallback(() => {
    setLightboxOpen(false)
  }, [])

  return {
    lightboxOpen,
    lightboxImages,
    lightboxIndex,
    handleOpenLightbox,
    handleCloseLightbox
  }
}

export default function NotesPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [filteredNotes, setFilteredNotes] = useState<Note[]>([])
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [user, setUser] = useState<{ id: string; email?: string } | null>(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [noteToDelete, setNoteToDelete] = useState<string | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [noteToEdit, setNoteToEdit] = useState<Note | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [reanalyzingNotes, setReanalyzingNotes] = useState<Set<string>>(new Set())
  const lightbox = useLightbox()
  const aiSearch = useAISearch()
  const router = useRouter()
  const supabase = createClient()

  const { notes, loading: notesLoading, createNote, updateNote, deleteNote, refetch } = useNotes()
  const { tags, loading: tagsLoading, refetch: refetchTags } = useTags()

  // Проверка аутентификации
  useEffect(() => {
    const checkUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        router.push('/login')
      } else {
        setUser(user)
      }
    }
    checkUser()
  }, [router, supabase])

  const filterNotes = useCallback(() => {
    let result: Note[]

    if (aiSearch.results) {
      result = selectedTags.length === 0
        ? aiSearch.results
        : aiSearch.results.filter((note: Note) => note.tags.some((tag: string) => selectedTags.includes(tag)))
    } else if (searchQuery.trim() === "" && selectedTags.length === 0) {
      result = notes
    } else {
      result = notes.filter((note) => {
        const matchesSearch = searchQuery.trim() === "" ||
          note.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
          (note.summary_ai && note.summary_ai.toLowerCase().includes(searchQuery.toLowerCase()))

        const matchesTags = selectedTags.length === 0 ||
          note.tags.some((tag) => selectedTags.includes(tag))

        return matchesSearch && matchesTags
      })
    }

    // Add reanalyzing state to notes
    const notesWithReanalyzingState = result.map(note => ({
      ...note,
      isAnalyzing: note.isAnalyzing || reanalyzingNotes.has(note.id),
      aiAnalysisFailed: reanalyzingNotes.has(note.id) ? false : note.aiAnalysisFailed
    }))

    setFilteredNotes(notesWithReanalyzingState)
  }, [notes, searchQuery, selectedTags, aiSearch.results, reanalyzingNotes])

  // Обновление отфильтрованных заметок при изменении данных
  useEffect(() => {
    filterNotes()
  }, [filterNotes])



  const handleSearchChange = (query: string) => {
    setSearchQuery(query)
    if (aiSearch.results) {
      aiSearch.clearResults()
    }
    if (aiSearch.error) {
      aiSearch.clearError()
    }
  }

  const handleAISearch = async (query: string) => {
    await aiSearch.search(query)
    setSearchQuery(query.trim())
  }

  const handleTagAdd = (tag: string) => {
    if (!selectedTags.includes(tag)) {
      setSelectedTags([...selectedTags, tag])
    }
  }

  const handleTagRemove = (tag: string) => {
    setSelectedTags(selectedTags.filter((t) => t !== tag))
  }

  const handleNoteSubmit = async (content: string, attachmentUrls?: string[]) => {
    const newNote = await createNote({ content, attachment_urls: attachmentUrls })
    if (newNote) {
      setTimeout(async () => {
        await refetchTags()
      }, 5000)
    }
  }

  const handleEditClick = (note: Note) => {
    setNoteToEdit(note)
    setEditDialogOpen(true)
  }

  const handleEditSave = async (
    noteId: string,
    content: string,
    attachmentsToAdd?: string[],
    attachmentsToRemove?: string[]
  ) => {
    setIsEditing(true)
    try {
      const updatedNote = await updateNote(noteId, {
        content,
        attachmentsToAdd,
        attachmentsToRemove
      })
      if (updatedNote) {
        setEditDialogOpen(false)
        setNoteToEdit(null)
        await refetchTags()
      }
    } finally {
      setIsEditing(false)
    }
  }

  const handleEditCancel = () => {
    setEditDialogOpen(false)
    setNoteToEdit(null)
  }

  const handleDeleteClick = (noteId: string) => {
    setNoteToDelete(noteId)
    setDeleteDialogOpen(true)
  }

  const handleReanalyzeClick = async (noteId: string) => {
    try {
      // Add note to reanalyzing set
      setReanalyzingNotes(prev => new Set(prev).add(noteId))

      const response = await fetch(`/api/notes/${noteId}/reanalyze`, {
        method: 'POST'
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Не удалось запустить повторный анализ')
      }

      // Poll for updates every 2 seconds for up to 30 seconds
      let attempts = 0
      const maxAttempts = 15

      const pollForUpdates = async () => {
        attempts++
        await refetch()

        // Check if the note now has AI results (analysis completed)
        const updatedNotes = await new Promise<Note[]>((resolve) => {
          // Small delay to ensure state is updated
          setTimeout(() => {
            resolve(notes)
          }, 100)
        })

        const updatedNote = updatedNotes.find(n => n.id === noteId)
        const hasAiResults = updatedNote && (updatedNote.summary_ai || updatedNote.tags.length > 0)

        if (hasAiResults) {
          // Analysis completed successfully, remove from reanalyzing set
          setReanalyzingNotes(prev => {
            const newSet = new Set(prev)
            newSet.delete(noteId)
            return newSet
          })
        } else if (attempts < maxAttempts) {
          setTimeout(pollForUpdates, 2000)
        } else {
          // Remove from reanalyzing set after timeout
          setReanalyzingNotes(prev => {
            const newSet = new Set(prev)
            newSet.delete(noteId)
            return newSet
          })
        }
      }

      // Start polling after a short delay
      setTimeout(pollForUpdates, 2000)

    } catch (error) {
      console.error('Error reanalyzing note:', error)
      // Remove from reanalyzing set on error
      setReanalyzingNotes(prev => {
        const newSet = new Set(prev)
        newSet.delete(noteId)
        return newSet
      })
    }
  }

  const handleDeleteConfirm = async () => {
    if (!noteToDelete) return

    setIsDeleting(true)
    try {
      const success = await deleteNote(noteToDelete)
      if (success) {
        setDeleteDialogOpen(false)
        setNoteToDelete(null)
        await refetchTags()
      }
    } finally {
      setIsDeleting(false)
    }
  }

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false)
    setNoteToDelete(null)
  }


  if (!user || notesLoading || tagsLoading) {
    return <div className="min-h-screen flex items-center justify-center">Загрузка...</div>
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Заголовок */}
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container max-w-4xl mx-auto px-4 flex h-14 items-center justify-between">
          <div className="flex items-center space-x-2">
            <FileText className="h-6 w-6" />
            <h1 className="text-lg font-semibold">Заметки</h1>
          </div>
          <div className="flex items-center space-x-2">
            <ThemeToggle />
            <form action="/auth/signout" method="post">
              <Button type="submit" variant="ghost" size="sm">
                Выйти
              </Button>
            </form>
          </div>
        </div>
      </header>

      <div className="container max-w-4xl mx-auto p-4 space-y-4">
        <SearchAndFilter
          searchQuery={searchQuery}
          onSearchChange={handleSearchChange}
          onAISearch={handleAISearch}
          selectedTags={selectedTags}
          onTagAdd={handleTagAdd}
          onTagRemove={handleTagRemove}
          availableTags={tags}
          isAISearching={aiSearch.isSearching}
        />

        <NoteComposer
          onSubmit={handleNoteSubmit}
          userEmail={user.email}
        />

        {aiSearch.error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {aiSearch.error}
            </AlertDescription>
          </Alert>
        )}

        {aiSearch.results && (
          <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg border">
            <div className="flex items-center space-x-2">
              <Sparkles className="h-4 w-4 text-primary" />
              <span className="text-sm font-medium">
                ИИ-поиск: найдено {aiSearch.results.length} заметок
              </span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                aiSearch.clearResults()
                setSearchQuery("")
              }}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        )}

        <NotesList
          notes={filteredNotes}
          onTagClick={handleTagAdd}
          onEditClick={handleEditClick}
          onDeleteClick={handleDeleteClick}
          onReanalyzeClick={handleReanalyzeClick}
          onOpenLightbox={lightbox.handleOpenLightbox}
          userEmail={user.email}
        />

        <EditNoteDialog
          note={noteToEdit}
          open={editDialogOpen}
          onOpenChange={handleEditCancel}
          onSave={handleEditSave}
          userEmail={user.email}
          isSaving={isEditing}
        />

        <DeleteNoteDialog
          open={deleteDialogOpen}
          onOpenChange={handleDeleteCancel}
          onConfirm={handleDeleteConfirm}
          isDeleting={isDeleting}
        />

        <ImageLightbox
          images={lightbox.lightboxImages}
          initialIndex={lightbox.lightboxIndex}
          open={lightbox.lightboxOpen}
          onClose={lightbox.handleCloseLightbox}
        />
      </div>
    </div>
  )
}
