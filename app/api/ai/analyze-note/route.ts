import { NextRequest } from 'next/server'
import { analyzeNote } from '@/lib/ai/analyze'
import { withAuth, validateRequestBody, validateString, createErrorResponse, createSuccessResponse } from '@/lib/api/middleware'
import { updateNoteWithTags } from '@/lib/data/notes'
import { DatabaseError } from '@/lib/data/types'

interface AnalyzeNoteRequest {
  noteId: string
  content: string
}

function validateAnalyzeNoteRequest(body: unknown): { isValid: boolean; error?: string; data?: AnalyzeNoteRequest } {
  if (!body || typeof body !== 'object') {
    return { isValid: false, error: 'Invalid request body' }
  }

  const bodyObj = body as Record<string, unknown>

  const noteIdError = validateString(bodyObj.noteId, 'Note ID')
  if (noteIdError) return { isValid: false, error: noteIdError }

  const contentError = validateString(bodyObj.content, 'Content')
  if (contentError) return { isValid: false, error: contentError }

  return {
    isValid: true,
    data: {
      noteId: (bodyObj.noteId as string).trim(),
      content: (bodyObj.content as string).trim()
    }
  }
}

export async function POST(request: NextRequest) {
  return withAuth(request, async (req, { user }) => {
    try {
      const body = await req.json()
      const validation = validateRequestBody(body, validateAnalyzeNoteRequest)

      if (!validation.isValid) {
        return createErrorResponse(validation.error!, 400)
      }

      const { noteId, content } = validation.data!

      const analysis = await analyzeNote(content)
      const result = await updateNoteWithTags(noteId, user.id, analysis.summary, analysis.tags)

      return createSuccessResponse({
        success: result.success,
        tags: result.tags,
        summary: result.summary
      })
    } catch (error) {
      console.error('Error in AI analyze note:', error)

      if (error instanceof DatabaseError) {
        if (error.message.includes('not found') || error.message.includes('access denied')) {
          return createErrorResponse('Note not found', 404)
        }
        return createErrorResponse('Failed to update note')
      }

      return createErrorResponse('Internal server error')
    }
  })
}
