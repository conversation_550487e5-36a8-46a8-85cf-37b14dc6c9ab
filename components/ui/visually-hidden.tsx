"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

/**
 * VisuallyHidden component hides content visually while keeping it accessible to screen readers.
 * This is useful for providing additional context to assistive technologies without affecting
 * the visual layout of the page.
 *
 * @example
 * <VisuallyHidden>
 *   <DialogTitle>Important Dialog</DialogTitle>
 * </VisuallyHidden>
 */
function VisuallyHidden({
  className,
  ...props
}: React.ComponentProps<"span">) {
  return (
    <span
      className={cn(
        "absolute w-px h-px p-0 -m-px overflow-hidden clip-[rect(0,0,0,0)] whitespace-nowrap border-0",
        className
      )}
      {...props}
    />
  )
}

export type VisuallyHiddenProps = React.ComponentProps<"span">

export { VisuallyHidden }
