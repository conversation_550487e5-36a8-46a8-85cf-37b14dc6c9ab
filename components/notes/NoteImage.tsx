import { AlertCircle } from "lucide-react"
import { useState, useCallback } from "react"
import Image from "next/image"

// Constants
const DEFAULT_ALT_TEXT = "Изображение заметки"
const DEFAULT_IMAGE_CLASSES = "w-full h-auto object-contain bg-muted"
const ERROR_TOOLTIP = "Ошибка анализа изображения"
const LOAD_ERROR_MESSAGE = "Не удалось загрузить изображение"

interface NoteImageProps {
  attachmentUrl: string
  alt?: string
  className?: string
  hasAnalysisError?: boolean
  onOpenLightbox?: () => void // Renamed from onPreview (back-compat: was onPreview)
}

export function NoteImage({ attachmentUrl, alt = DEFAULT_ALT_TEXT, className, hasAnalysisError, onOpenLightbox }: NoteImageProps) {
  const [hasLoadError, setHasLoadError] = useState(false)

  const handleImageLoadError = useCallback(() => {
    setHasLoadError(true)
  }, [])

  const handleImageClick = useCallback(() => {
    if (onOpenLightbox) {
      onOpenLightbox()
    }
  }, [onOpenLightbox])

  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault()
      handleImageClick()
    }
  }, [handleImageClick])

  return (
    <div className="rounded-lg overflow-hidden border relative">
      {hasAnalysisError && (
        <div className="absolute top-2 right-2 z-10">
          <div className="bg-destructive text-destructive-foreground rounded-full p-1" title={ERROR_TOOLTIP}>
            <AlertCircle className="h-4 w-4" />
          </div>
        </div>
      )}
      {hasLoadError ? (
        <div className="p-4 bg-muted text-center text-muted-foreground">
          <div className="text-sm">{LOAD_ERROR_MESSAGE}</div>
        </div>
      ) : (
        <div className="relative w-full max-h-[32rem] overflow-hidden">
          <Image
            src={attachmentUrl}
            alt={alt}
            className={`${className || DEFAULT_IMAGE_CLASSES} ${onOpenLightbox ? 'cursor-pointer' : ''}`}
            width={0}
            height={0}
            sizes="(max-width: 640px) 100vw, (max-width: 1024px) 75vw, 50vw"
            style={{ width: '100%', height: 'auto', maxHeight: '32rem' }}
            onError={handleImageLoadError}
            onClick={handleImageClick}
            tabIndex={onOpenLightbox ? 0 : undefined}
            onKeyDown={onOpenLightbox ? handleKeyDown : undefined}
            unoptimized={attachmentUrl.startsWith('blob:')}
          />
        </div>
      )}
    </div>
  )
}
