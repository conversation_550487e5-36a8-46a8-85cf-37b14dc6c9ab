import { FileText, FileType, File as FileIcon } from "lucide-react"
import { extractFilenameFromUrl } from "@/lib/utils/url"

interface NoteDocumentProps {
  attachmentUrl: string
  className?: string
}

// Иконки для разных типов документов
const getDocumentIcon = (filename: string) => {
  const extension = filename.split('.').pop()?.toLowerCase()

  switch (extension) {
    case 'pdf':
      return <FileType className="h-8 w-8 text-red-600 flex-shrink-0" />
    case 'txt':
      return <FileText className="h-8 w-8 text-blue-600 flex-shrink-0" />
    case 'md':
      return <FileText className="h-8 w-8 text-purple-600 flex-shrink-0" />
    default:
      return <FileIcon className="h-8 w-8 text-muted-foreground flex-shrink-0" />
  }
}

export function NoteDocument({ attachmentUrl, className = "" }: NoteDocumentProps) {
  const filename = extractFilenameFromUrl(attachmentUrl)
  const extension = filename.split('.').pop()?.toLowerCase() || ''

  // Определяем максимальную длину имени файла в зависимости от расширения
  const maxLength = 40
  const displayName = filename.length > maxLength
    ? `${filename.substring(0, maxLength - extension.length - 4)}...${extension}`
    : filename

  return (
    <a
      href={attachmentUrl}
      target="_blank"
      rel="noopener noreferrer"
      className={`flex items-center gap-3 p-4 bg-muted rounded-lg hover:bg-muted/80 transition-colors group ${className}`}
      title={filename} // Показываем полное имя файла при наведении
    >
      {getDocumentIcon(filename)}
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium truncate group-hover:text-primary transition-colors">
          {displayName}
        </p>
        <p className="text-xs text-muted-foreground">
          {extension.toUpperCase()} документ
        </p>
      </div>
    </a>
  )
}
