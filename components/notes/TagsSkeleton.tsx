import { Spark<PERSON> } from "lucide-react"
import { Skeleton } from "@/components/ui/skeleton"

export function TagsSkeleton() {
  return (
    <div className="flex flex-wrap gap-1 items-center">
      <div className="flex items-center gap-2 text-xs text-muted-foreground mb-1">
        <Sparkles className="h-3 w-3 animate-spin text-primary" />
        <span>ИИ генерирует теги...</span>
      </div>
      <div className="flex gap-1 flex-wrap">
        {[1, 2, 3].map((i) => (
          <Skeleton
            key={i}
            className={`h-5 rounded-full ${
              i === 1 ? 'w-16' : i === 2 ? 'w-20' : 'w-14'
            }`}
          />
        ))}
      </div>
    </div>
  )
}
