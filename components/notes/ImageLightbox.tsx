"use client"

import * as React from "react"
import { useState, useEffect, useCallback, useRef } from "react"
import { ChevronLeft, ChevronRight, Download, AlertCircle, RotateCcw, ZoomIn, ZoomOut } from "lucide-react"
import Image from "next/image"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Dialog, DialogContent, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { VisuallyHidden } from "@/components/ui/visually-hidden"
import { cn } from "@/lib/utils"
import { logger } from "@/lib/utils/logger"

// Constants for centralized class strings and aria text
const LIGHTBOX_CLASSES = {
  buttonBase: "h-10 w-10 rounded-full bg-black/50 hover:bg-black/70 dark:bg-white/15 dark:hover:bg-white/25 backdrop-blur-sm text-white hover:text-white dark:text-white dark:hover:text-white border-none focus:outline-none focus:ring-2 focus:ring-white/30 transition-all duration-200",
  dialogContent: "!bg-transparent !shadow-none p-0 flex items-center justify-center max-w-[100vw] max-h-[100vh] w-full h-full border-none",
  imageContainer: "relative w-full h-full flex items-center justify-center overflow-visible",
  image: "max-w-full max-h-full object-contain outline-none focus:outline-none focus-visible:outline-none transition-transform duration-300 ease-out z-10 relative",
  loadingSpinner: "animate-spin rounded-full h-8 w-8 border-2 border-white border-t-transparent",
  errorContainer: "flex flex-col items-center justify-center p-8 text-white",
  header: "absolute top-4 left-4 right-4 z-30 flex items-center justify-between opacity-90 hover:opacity-100 transition-opacity duration-200",
  navigation: "absolute left-4 top-1/2 -translate-y-1/2 z-30 opacity-80 hover:opacity-100 transition-opacity duration-200",
  navigationRight: "absolute right-4 top-1/2 -translate-y-1/2 z-30 opacity-80 hover:opacity-100 transition-opacity duration-200",
  bottomPanel: "absolute bottom-4 left-4 right-4 z-30 flex items-center justify-between opacity-90 hover:opacity-100 transition-opacity duration-200",
  dotsContainer: "flex gap-2",
  controlsContainer: "flex items-center gap-2",
  dot: "w-2 h-2 rounded-full transition-all duration-200",
  dotActive: "bg-white",
  dotInactive: "bg-white/50 hover:bg-white/75"
}

const LIGHTBOX_ARIA = {
  title: "Просмотр изображения",
  description: "Полноразмерный просмотр изображения. Используйте клавиши стрелок или кнопки навигации для перехода между изображениями. Двойной клик для масштабирования.",
  downloadButton: "Скачать изображение",
  closeButton: "Закрыть просмотр",
  previousButton: "Предыдущее изображение",
  nextButton: "Следующее изображение",
  zoomInButton: "Увеличить",
  zoomOutButton: "Уменьшить",
  imageAlt: (index: number, total: number) => `Изображение ${index + 1} из ${total}`,
  goToImage: (index: number) => `Перейти к изображению ${index + 1}`,
  errorMessage: "Не удалось загрузить изображение",
  retryButton: "Повторить"
}

interface ImageLightboxProps {
  images: string[]          // URLs for the current note's images
  initialIndex: number      // which image to show first
  open: boolean             // controlled visibility
  onClose: () => void       // callback to parent
}

// Custom hook for lightbox navigation and zoom logic
function useLightboxNavigation(
  images: string[],
  initialIndex: number,
  open: boolean
) {
  const [currentIndex, setCurrentIndex] = useState(initialIndex)
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [imageUrl, setImageUrl] = useState("")

  // Zoom and pan states
  const [scale, setScale] = useState(1)
  const [translateX, setTranslateX] = useState(0)
  const [translateY, setTranslateY] = useState(0)
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const [dragStartTranslate, setDragStartTranslate] = useState({ x: 0, y: 0 })

  const containerRef = useRef<HTMLDivElement>(null)

  // Reset current index and states when lightbox is (re)opened
  useEffect(() => {
    if (open) {
      setCurrentIndex(initialIndex)
      setIsLoading(true)
      setHasError(false)
      setImageUrl(images[initialIndex] || "")
      // Reset zoom and pan
      setScale(1)
      setTranslateX(0)
      setTranslateY(0)
      setIsDragging(false)
    }
  }, [open, initialIndex, images])

  // Reset states when image changes during navigation
  useEffect(() => {
    if (open && images[currentIndex]) {
      setIsLoading(true)
      setHasError(false)
      setImageUrl(images[currentIndex])
      // Reset zoom and pan when changing images
      setScale(1)
      setTranslateX(0)
      setTranslateY(0)
      setIsDragging(false)
    }
  }, [currentIndex, open, images])

  const handlePrevious = useCallback(() => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1)
    }
  }, [currentIndex])

  const handleNext = useCallback(() => {
    if (currentIndex < images.length - 1) {
      setCurrentIndex(currentIndex + 1)
    }
  }, [currentIndex, images.length])

  const handleImageLoad = useCallback(() => {
    setIsLoading(false)
    setHasError(false)
  }, [])

  const handleImageError = useCallback(() => {
    setIsLoading(false)
    setHasError(true)
  }, [])

  // Zoom functions
  const handleZoomIn = useCallback(() => {
    setScale(prev => Math.min(prev * 1.5, 5))
  }, [])

  const handleZoomOut = useCallback(() => {
    setScale(prev => {
      const newScale = prev / 1.5
      if (newScale <= 1) {
        setTranslateX(0)
        setTranslateY(0)
        return 1
      }
      return newScale
    })
  }, [])

  const handleDoubleClick = useCallback(() => {
    if (scale > 1) {
      setScale(1)
      setTranslateX(0)
      setTranslateY(0)
    } else {
      setScale(2)
    }
  }, [scale])

  // Mouse wheel zoom
  const handleWheel = useCallback((event: WheelEvent) => {
    if (!open) return
    event.preventDefault()

    const delta = event.deltaY > 0 ? 0.9 : 1.1
    setScale(prev => {
      const newScale = Math.max(0.5, Math.min(prev * delta, 5))
      if (newScale <= 1) {
        setTranslateX(0)
        setTranslateY(0)
        return 1
      }
      return newScale
    })
  }, [open])

  // Pan functionality
  const handleMouseDown = useCallback((event: React.MouseEvent) => {
    if (scale <= 1) return
    event.preventDefault()
    setIsDragging(true)
    setDragStart({ x: event.clientX, y: event.clientY })
    setDragStartTranslate({ x: translateX, y: translateY })
  }, [scale, translateX, translateY])

  const handleMouseMove = useCallback((event: MouseEvent) => {
    if (!isDragging || scale <= 1) return
    event.preventDefault()

    const deltaX = event.clientX - dragStart.x
    const deltaY = event.clientY - dragStart.y

    setTranslateX(dragStartTranslate.x + deltaX)
    setTranslateY(dragStartTranslate.y + deltaY)
  }, [isDragging, scale, dragStart, dragStartTranslate])

  const handleMouseUp = useCallback(() => {
    setIsDragging(false)
  }, [])

  // Handle keyboard navigation and zoom
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!open) return

      switch (event.key) {
        case "ArrowLeft":
          event.preventDefault()
          handlePrevious()
          break
        case "ArrowRight":
          event.preventDefault()
          handleNext()
          break
        case "Escape":
          event.preventDefault()
          // Will be handled by dialog
          break
        case "+":
        case "=":
          event.preventDefault()
          handleZoomIn()
          break
        case "-":
          event.preventDefault()
          handleZoomOut()
          break
      }
    }

    document.addEventListener("keydown", handleKeyDown)
    return () => document.removeEventListener("keydown", handleKeyDown)
  }, [open, handlePrevious, handleNext, handleZoomIn, handleZoomOut])

  // Mouse events for pan
  useEffect(() => {
    if (isDragging) {
      document.addEventListener("mousemove", handleMouseMove)
      document.addEventListener("mouseup", handleMouseUp)
      return () => {
        document.removeEventListener("mousemove", handleMouseMove)
        document.removeEventListener("mouseup", handleMouseUp)
      }
    }
  }, [isDragging, handleMouseMove, handleMouseUp])

  // Mouse wheel for zoom
  useEffect(() => {
    const container = containerRef.current
    if (container && open) {
      container.addEventListener("wheel", handleWheel, { passive: false })
      return () => container.removeEventListener("wheel", handleWheel)
    }
  }, [open, handleWheel])

  return {
    currentIndex,
    setCurrentIndex,
    isLoading,
    hasError,
    setIsLoading,
    setHasError,
    imageUrl,
    setImageUrl,
    handlePrevious,
    handleNext,
    handleImageLoad,
    handleImageError,
    scale,
    translateX,
    translateY,
    isDragging,
    containerRef,
    handleZoomIn,
    handleZoomOut,
    handleDoubleClick,
    handleMouseDown
  }
}

// Sub-component for lightbox control buttons
interface LightboxButtonProps {
  variant: "download" | "close" | "previous" | "next" | "zoomIn" | "zoomOut"
  onClick: () => void
  disabled?: boolean
  className?: string
}

function LightboxButton({ variant, onClick, disabled, className }: LightboxButtonProps) {
  const iconMap = {
    download: <Download className="h-4 w-4" />,
    close: (
      <svg
        width="15"
        height="15"
        viewBox="0 0 15 15"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M11.7816 4.03157C12.0062 3.80702 12.0062 3.44295 11.7816 3.2184C11.5571 2.99385 11.193 2.99385 10.9685 3.2184L7.50005 6.68682L4.03164 3.2184C3.80708 2.99385 3.44301 2.99385 3.21846 3.2184C2.99391 3.44295 2.99391 3.80702 3.21846 4.03157L6.68688 7.49999L3.21846 10.9684C2.99391 11.193 2.99391 11.557 3.21846 11.7816C3.44301 12.0061 3.80708 12.0061 4.03164 11.7816L7.50005 8.31316L10.9685 11.7816C11.193 12.0061 11.5571 12.0061 11.7816 11.7816C12.0062 11.557 12.0062 11.193 11.7816 10.9684L8.31322 7.49999L11.7816 4.03157Z"
          fill="currentColor"
          fillRule="evenodd"
          clipRule="evenodd"
        />
      </svg>
    ),
    previous: <ChevronLeft className="h-6 w-6" />,
    next: <ChevronRight className="h-6 w-6" />,
    zoomIn: <ZoomIn className="h-4 w-4" />,
    zoomOut: <ZoomOut className="h-4 w-4" />
  }

  const ariaLabelMap = {
    download: LIGHTBOX_ARIA.downloadButton,
    close: LIGHTBOX_ARIA.closeButton,
    previous: LIGHTBOX_ARIA.previousButton,
    next: LIGHTBOX_ARIA.nextButton,
    zoomIn: LIGHTBOX_ARIA.zoomInButton,
    zoomOut: LIGHTBOX_ARIA.zoomOutButton
  }

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={onClick}
      disabled={disabled}
      className={cn(
        LIGHTBOX_CLASSES.buttonBase,
        disabled && "opacity-50 cursor-not-allowed",
        className
      )}
      aria-label={ariaLabelMap[variant]}
    >
      {iconMap[variant]}
    </Button>
  )
}

// Sub-component for image display area
interface ImageStageProps {
  currentImage: string
  currentIndex: number
  totalImages: number
  isLoading: boolean
  hasError: boolean
  onImageLoad: () => void
  onImageError: () => void
  onRetry: () => void
  scale: number
  translateX: number
  translateY: number
  isDragging: boolean
  containerRef: React.RefObject<HTMLDivElement | null>
  onDoubleClick: () => void
  onMouseDown: (event: React.MouseEvent) => void
}

function ImageStage({
  currentImage,
  currentIndex,
  totalImages,
  isLoading,
  hasError,
  onImageLoad,
  onImageError,
  onRetry,
  scale,
  translateX,
  translateY,
  isDragging,
  containerRef,
  onDoubleClick,
  onMouseDown
}: ImageStageProps) {
  if (hasError) {
    return (
      <div className={LIGHTBOX_CLASSES.errorContainer}>
        <AlertCircle className="h-12 w-12 mb-4 text-red-400" />
        <p className="text-lg mb-4">{LIGHTBOX_ARIA.errorMessage}</p>
        <Button
          onClick={onRetry}
          variant="outline"
          className="text-white border-white hover:bg-white hover:text-black"
        >
          <RotateCcw className="h-4 w-4 mr-2" />
          {LIGHTBOX_ARIA.retryButton}
        </Button>
      </div>
    )
  }

  return (
    <div
      ref={containerRef}
      className={cn(
        LIGHTBOX_CLASSES.imageContainer,
        scale > 1 && "cursor-grab",
        isDragging && "cursor-grabbing"
      )}
      onMouseDown={onMouseDown}
      onClick={(e) => e.stopPropagation()}
      role="presentation"
    >
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center z-10">
          <div className={LIGHTBOX_CLASSES.loadingSpinner} />
        </div>
      )}
      <Image
        key={currentImage}
        src={currentImage}
        alt={LIGHTBOX_ARIA.imageAlt(currentIndex, totalImages)}
        className={LIGHTBOX_CLASSES.image}
        style={{
          transform: `scale(${scale}) translate(${translateX / scale}px, ${translateY / scale}px)`,
          maxHeight: "100vh",
          maxWidth: "100vw"
        }}
        width={1920}
        height={1080}
        sizes="100vw"
        onLoad={onImageLoad}
        onError={(e) => {
          logger.error("ImageLightbox: Image load error:", currentImage, e)
          onImageError()
        }}
        onDoubleClick={onDoubleClick}
        tabIndex={-1}
        draggable={false}
      />
    </div>
  )
}

export default function ImageLightbox({
  images,
  initialIndex,
  open,
  onClose
}: ImageLightboxProps) {
  const navigation = useLightboxNavigation(images, initialIndex, open)

  const handleRetry = useCallback(() => {
    navigation.setIsLoading(true)
    navigation.setHasError(false)
    // Force image reload by adding timestamp to URL
    const newUrl = `${images[navigation.currentIndex]}?t=${Date.now()}`
    navigation.setImageUrl(newUrl)
  }, [navigation, images])

  const handleDownload = useCallback(async () => {
    if (!images[navigation.currentIndex]) return

    try {
      const response = await fetch(images[navigation.currentIndex])
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement("a")
      link.href = url

      // Extract file extension from URL
      const imageUrl = images[navigation.currentIndex]
      const urlParts = imageUrl.split('?')[0] // Remove query parameters
      const fileName = urlParts.split('/').pop() || ''
      const fileExtension = fileName.includes('.') ? fileName.split('.').pop() : 'jpg'

      link.download = `image-${navigation.currentIndex + 1}.${fileExtension}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error("Download failed:", error)
    }
  }, [images, navigation.currentIndex])

  // Handle background click to close
  const handleBackgroundClick = useCallback((event: React.MouseEvent) => {
    if (event.target === event.currentTarget) {
      onClose()
    }
  }, [onClose])

  if (!open || !images.length) return null

  const currentImage = navigation.imageUrl || images[navigation.currentIndex]
  const isFirstImage = navigation.currentIndex === 0
  const isLastImage = navigation.currentIndex === images.length - 1

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent
        className={LIGHTBOX_CLASSES.dialogContent}
        showCloseButton={false}
        onClick={handleBackgroundClick}
      >
        <VisuallyHidden>
          <DialogTitle>{LIGHTBOX_ARIA.title}</DialogTitle>
        </VisuallyHidden>
        <VisuallyHidden>
          <DialogDescription>
            {LIGHTBOX_ARIA.description}
          </DialogDescription>
        </VisuallyHidden>

        {/* Header with counter and close button */}
        <div className={LIGHTBOX_CLASSES.header}>
          <div className="flex items-center gap-2">
            <span className="text-white text-sm font-medium">
              {navigation.currentIndex + 1} / {images.length}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <LightboxButton
              variant="close"
              onClick={onClose}
            />
          </div>
        </div>

        {/* Main image container */}
        <ImageStage
          currentImage={currentImage}
          currentIndex={navigation.currentIndex}
          totalImages={images.length}
          isLoading={navigation.isLoading}
          hasError={navigation.hasError}
          onImageLoad={navigation.handleImageLoad}
          onImageError={navigation.handleImageError}
          onRetry={handleRetry}
          scale={navigation.scale}
          translateX={navigation.translateX}
          translateY={navigation.translateY}
          isDragging={navigation.isDragging}
          containerRef={navigation.containerRef}
          onDoubleClick={navigation.handleDoubleClick}
          onMouseDown={navigation.handleMouseDown}
        />

        {/* Side navigation buttons */}
        {images.length > 1 && (
          <>
            <LightboxButton
              variant="previous"
              onClick={navigation.handlePrevious}
              disabled={isFirstImage}
              className={LIGHTBOX_CLASSES.navigation}
            />
            <LightboxButton
              variant="next"
              onClick={navigation.handleNext}
              disabled={isLastImage}
              className={LIGHTBOX_CLASSES.navigationRight}
            />
          </>
        )}

        {/* Bottom panel with dots and controls */}
        {images.length > 0 && (
          <div className={LIGHTBOX_CLASSES.bottomPanel}>
            {/* Dots */}
            <div className={LIGHTBOX_CLASSES.dotsContainer}>
              {images.length > 1 && images.map((_, index) => (
                <button
                  key={index}
                  type="button"
                  onClick={() => navigation.setCurrentIndex(index)}
                  className={cn(
                    LIGHTBOX_CLASSES.dot,
                    index === navigation.currentIndex
                      ? LIGHTBOX_CLASSES.dotActive
                      : LIGHTBOX_CLASSES.dotInactive
                  )}
                  aria-label={LIGHTBOX_ARIA.goToImage(index)}
                />
              ))}
            </div>

            {/* Controls */}
            <div className={LIGHTBOX_CLASSES.controlsContainer}>
              <LightboxButton
                variant="zoomOut"
                onClick={navigation.handleZoomOut}
                disabled={navigation.scale <= 1}
              />
              <LightboxButton
                variant="zoomIn"
                onClick={navigation.handleZoomIn}
                disabled={navigation.scale >= 5}
              />
              <LightboxButton
                variant="download"
                onClick={handleDownload}
              />
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
