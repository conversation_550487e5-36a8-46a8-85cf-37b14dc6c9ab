import { useState, useEffect, useRef } from "react"
import { Edit3, Save, X, Image as ImageIcon, Trash2, RotateCcw, <PERSON>clip, File, Loader2, FileText, FileType } from "lucide-react"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import ReactMarkdown from "react-markdown"
import remarkGfm from "remark-gfm"
import { Note } from "@/types/notes"
import { useMultipleFileUpload } from "@/hooks/useFileUpload"
import { ATTACHMENT_LIMITS, UPLOAD_CONFIG } from "@/lib/config/attachments"
import { FILE_CATEGORIES, FILE_TYPE_CONFIG, TOTAL_ATTACHMENT_LIMIT } from "@/lib/config/fileTypes"
import { logger } from "@/lib/utils/logger"
import { extractFilenameFromUrl } from "@/lib/utils/url"
import { isImageUrl } from "@/lib/utils/file-type"

interface EditNoteDialogProps {
  note: Note | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onSave: (noteId: string, content: string, attachmentsToAdd?: string[], attachmentsToRemove?: string[]) => Promise<void>
  userEmail?: string
  isSaving?: boolean
}

export function EditNoteDialog({
  note,
  open,
  onOpenChange,
  onSave,
  userEmail,
  isSaving = false
}: EditNoteDialogProps) {
  const [content, setContent] = useState("")
  const [isMarkdownMode, setIsMarkdownMode] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)
  const [attachmentsToRemove, setAttachmentsToRemove] = useState<string[]>([])
  const [fileError, setFileError] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const documentInputRef = useRef<HTMLInputElement>(null)

  // Calculate current attachments (original minus removed)
  const currentAttachments = note?.attachments.filter(
    attachment => !attachmentsToRemove.includes(attachment.id)
  ) || []

  // File upload hooks for images and documents
  const {
    selectedFiles: selectedImages,
    uploadingFiles: uploadingImages,
    addFile: addImage,
    removeFile: removeImage,
    uploadAll: uploadImages
  } = useMultipleFileUpload(ATTACHMENT_LIMITS.MAX_PER_NOTE, FILE_CATEGORIES.IMAGE)

  const {
    selectedFiles: selectedDocuments,
    uploadingFiles: uploadingDocuments,
    addFile: addDocument,
    removeFile: removeDocument,
    uploadAll: uploadDocuments
  } = useMultipleFileUpload(ATTACHMENT_LIMITS.MAX_PER_NOTE, FILE_CATEGORIES.DOCUMENT)

  // Combined state helpers
  const totalNewAttachments = selectedImages.length + selectedDocuments.length
  const totalAttachments = currentAttachments.length + totalNewAttachments
  const canAddMoreTotal = totalAttachments < TOTAL_ATTACHMENT_LIMIT
  const uploadingFiles = uploadingImages || uploadingDocuments
  const selectedFiles = [...selectedImages, ...selectedDocuments]

  // Обновляем содержимое при изменении заметки
  useEffect(() => {
    if (note) {
      setContent(note.content)
      setHasChanges(false)
      setAttachmentsToRemove([])
      setFileError(null)
    }
  }, [note])

  // Очищаем выбранные файлы при закрытии диалога
  useEffect(() => {
    return () => {
      // Cleanup function - очищаем blob URLs при размонтировании
      selectedImages.forEach(file => {
        URL.revokeObjectURL(file.previewUrl)
      })
      selectedDocuments.forEach(file => {
        URL.revokeObjectURL(file.previewUrl)
      })
    }
  }, [selectedImages, selectedDocuments])

  // Отслеживаем изменения в содержимом и вложениях
  useEffect(() => {
    if (note) {
      const hasContentChanges = content !== note.content
      const hasAttachmentChanges = attachmentsToRemove.length > 0 || totalNewAttachments > 0
      setHasChanges(hasContentChanges || hasAttachmentChanges)
    }
  }, [content, note, attachmentsToRemove, totalNewAttachments])

  const handleSave = async () => {
    if (!note || !hasChanges || isSaving) return

    // Разрешаем сохранение с пустым текстом, если есть изменения в вложениях
    const hasAttachmentChanges = attachmentsToRemove.length > 0 || selectedFiles.length > 0
    const hasContentChanges = content !== note.content
    const canSaveWithoutContent = hasAttachmentChanges && !hasContentChanges

    if (!content.trim() && !canSaveWithoutContent) return

    try {
      setFileError(null)

      // Upload new files if any
      let attachmentUrls: string[] = []
      if (totalNewAttachments > 0) {
        try {
          // Upload images and documents separately
          const [imageUrls, documentUrls] = await Promise.all([
            selectedImages.length > 0 ? uploadImages() : Promise.resolve([]),
            selectedDocuments.length > 0 ? uploadDocuments() : Promise.resolve([])
          ])
          attachmentUrls = [...imageUrls, ...documentUrls]
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Ошибка загрузки файлов'
          setFileError(errorMessage)
          return
        }
      }

      // Save note with attachments
      // Use original content as fallback if current content is empty
      const finalContent = content.trim() || note.content
      await onSave(
        note.id,
        finalContent,
        attachmentUrls.length > 0 ? attachmentUrls : undefined,
        attachmentsToRemove.length > 0 ? attachmentsToRemove : undefined
      )

      // Clear selected files after successful save
      selectedImages.forEach(file => {
        URL.revokeObjectURL(file.previewUrl)
        removeImage(file.id)
      })
      selectedDocuments.forEach(file => {
        URL.revokeObjectURL(file.previewUrl)
        removeDocument(file.id)
      })

      onOpenChange(false)
    } catch (error) {
      // Ошибка будет обработана в родительском компоненте
      logger.error('Error saving note:', error)
    }
  }

  const handleCancel = () => {
    if (note) {
      setContent(note.content)
      setHasChanges(false)
    }
    setIsMarkdownMode(false)
    setAttachmentsToRemove([])
    setFileError(null)

    // Очищаем выбранные файлы и их blob URLs
    selectedImages.forEach(file => {
      URL.revokeObjectURL(file.previewUrl)
      removeImage(file.id)
    })
    selectedDocuments.forEach(file => {
      URL.revokeObjectURL(file.previewUrl)
      removeDocument(file.id)
    })

    onOpenChange(false)
  }

  const handleRemoveAttachment = (attachmentId: string) => {
    setAttachmentsToRemove(prev => [...prev, attachmentId])
    setFileError(null)
  }

  const handleRestoreAttachment = (attachmentId: string) => {
    setAttachmentsToRemove(prev => prev.filter(id => id !== attachmentId))
  }

  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files || files.length === 0) return

    setFileError(null)

    // Validate total attachments limit
    const remainingSlots = TOTAL_ATTACHMENT_LIMIT - totalAttachments
    if (files.length > remainingSlots) {
      setFileError(`Можно добавить еще ${remainingSlots} файлов. Выбрано: ${files.length}`)
      return
    }

    // Add each selected file
    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      const result = addImage(file)

      if (!result.success) {
        setFileError(result.error || 'Ошибка при выборе файла')
        break
      }
    }
  }

  const handleDocumentSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files || files.length === 0) return

    setFileError(null)

    // Validate total attachments limit
    const remainingSlots = TOTAL_ATTACHMENT_LIMIT - totalAttachments
    if (files.length > remainingSlots) {
      setFileError(`Можно добавить еще ${remainingSlots} файлов. Выбрано: ${files.length}`)
      return
    }

    // Add each selected file
    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      const result = addDocument(file)

      if (!result.success) {
        setFileError(result.error || 'Ошибка при выборе файла')
        break
      }
    }
  }

  const handleRemoveNewFile = (fileId: string) => {
    // Find and remove from appropriate list
    const imageFile = selectedImages.find(f => f.id === fileId)
    if (imageFile) {
      removeImage(fileId)
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    } else {
      removeDocument(fileId)
      if (documentInputRef.current) {
        documentInputRef.current.value = ''
      }
    }
    setFileError(null)
  }

  // Helper to check if file is an image
  const isImageFile = (file: File): boolean => {
    return FILE_TYPE_CONFIG[FILE_CATEGORIES.IMAGE].allowedMimeTypes.includes(file.type)
  }

  // Helper to get document icon
  const getDocumentIcon = (filename: string) => {
    const extension = filename.split('.').pop()?.toLowerCase()

    switch (extension) {
      case 'pdf':
        return <FileType className="h-4 w-4 text-red-600" />
      case 'txt':
        return <FileText className="h-4 w-4 text-blue-600" />
      case 'md':
        return <FileText className="h-4 w-4 text-purple-600" />
      default:
        return <File className="h-4 w-4 text-muted-foreground" />
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("ru-RU", {
      day: "numeric",
      month: "short",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  if (!note) return null

  // Разрешаем сохранение если есть изменения в вложениях, даже если текст пустой
  const hasAttachmentChanges = attachmentsToRemove.length > 0 || totalNewAttachments > 0
  const hasContentChanges = content !== (note?.content || '')
  const canSaveWithoutContent = hasAttachmentChanges && !hasContentChanges

  const isDisabled = (!content.trim() && !canSaveWithoutContent) || !hasChanges || isSaving || uploadingFiles

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Edit3 className="h-5 w-5" />
            <span>Редактировать заметку</span>
          </DialogTitle>
          <DialogDescription>
            Внесите изменения в содержимое заметки и нажмите &quot;Сохранить&quot;
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-hidden flex flex-col space-y-4">
          {/* Информация о заметке */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Avatar className="h-8 w-8">
                <AvatarFallback>{userEmail?.[0]?.toUpperCase() || 'U'}</AvatarFallback>
              </Avatar>
              <div>
                <div className="text-sm font-medium">Вы</div>
                <div className="text-xs text-muted-foreground">
                  Создано: {formatDate(note.created_at)}
                  {note.updated_at !== note.created_at && (
                    <span> • Изменено: {formatDate(note.updated_at)}</span>
                  )}
                </div>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMarkdownMode(!isMarkdownMode)}
              className={isMarkdownMode ? "bg-muted" : ""}
            >
              <Edit3 className="h-4 w-4 mr-1" />
              Markdown
            </Button>
          </div>

          {/* Поле редактирования */}
          <div className="flex-1 overflow-hidden flex flex-col space-y-3">
            <Textarea
              placeholder={isMarkdownMode ? "Введите текст с поддержкой Markdown..." : "Введите содержимое заметки..."}
              value={content}
              onChange={(e) => setContent(e.target.value)}
              className="flex-1 min-h-[200px] resize-none"
            />

            {/* Предварительный просмотр Markdown */}
            {isMarkdownMode && content && (
              <div className="flex-1 border rounded-md p-3 bg-muted/50 overflow-auto">
                <div className="text-xs text-muted-foreground mb-2">Предварительный просмотр:</div>
                <div className="prose max-w-none">
                  <ReactMarkdown remarkPlugins={[remarkGfm]}>
                    {content}
                  </ReactMarkdown>
                </div>
              </div>
            )}
          </div>

          {/* Управление вложениями */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium">Вложения ({totalAttachments}/{TOTAL_ATTACHMENT_LIMIT})</h4>
              <div className="flex space-x-2">
                <input
                  ref={fileInputRef}
                  type="file"
                  accept={FILE_TYPE_CONFIG[FILE_CATEGORIES.IMAGE].accept}
                  multiple={UPLOAD_CONFIG.MULTIPLE}
                  onChange={handleImageSelect}
                  className="hidden"
                  disabled={!canAddMoreTotal || uploadingFiles}
                />
                <input
                  ref={documentInputRef}
                  type="file"
                  accept={FILE_TYPE_CONFIG[FILE_CATEGORIES.DOCUMENT].accept}
                  multiple={UPLOAD_CONFIG.MULTIPLE}
                  onChange={handleDocumentSelect}
                  className="hidden"
                  disabled={!canAddMoreTotal || uploadingFiles}
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => documentInputRef.current?.click()}
                  disabled={!canAddMoreTotal || uploadingFiles}
                >
                  {uploadingDocuments ? (
                    <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                  ) : (
                    <Paperclip className="h-4 w-4 mr-1" />
                  )}
                  Документ
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={!canAddMoreTotal || uploadingFiles}
                >
                  {uploadingImages ? (
                    <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                  ) : (
                    <ImageIcon className="h-4 w-4 mr-1" />
                  )}
                  Изображение
                </Button>
              </div>
            </div>

            {/* Ошибки загрузки файлов */}
            {fileError && (
              <Alert>
                <AlertDescription>{fileError}</AlertDescription>
              </Alert>
            )}

            {/* Существующие вложения */}
            {currentAttachments.length > 0 && (
              <div className="space-y-2">
                <div className="text-xs text-muted-foreground">Текущие файлы:</div>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {currentAttachments.map((attachment) => {
                    const isImage = isImageUrl(attachment.url)
                    const filename = extractFilenameFromUrl(attachment.url)

                    return (
                      <div key={attachment.id} className="relative group h-20">
                        {isImage ? (
                          <Image
                            src={attachment.url}
                            alt="Attachment"
                            fill
                            sizes="(max-width: 640px) 50vw, 200px"
                            className="object-cover rounded border"
                          />
                        ) : (
                          <div className="flex flex-col items-center justify-center w-full h-full bg-muted rounded border p-2">
                            {getDocumentIcon(filename)}
                            <span className="text-xs text-muted-foreground truncate max-w-full mt-1">
                              {filename}
                            </span>
                          </div>
                        )}
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                          onClick={() => handleRemoveAttachment(attachment.id)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    )
                  })}
                </div>
              </div>
            )}

            {/* Удаленные вложения (для восстановления) */}
            {attachmentsToRemove.length > 0 && (
              <div className="space-y-2">
                <div className="text-xs text-muted-foreground">Удаленные изображения (можно восстановить):</div>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {note?.attachments
                    .filter(attachment => attachmentsToRemove.includes(attachment.id))
                    .map((attachment) => (
                      <div key={attachment.id} className="relative group h-20">
                        <Image
                          src={attachment.url}
                          alt="Removed attachment"
                          fill
                          sizes="(max-width: 640px) 50vw, 200px"
                          className="object-cover rounded border opacity-50"
                        />
                        <Button
                          type="button"
                          variant="secondary"
                          size="sm"
                          className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                          onClick={() => handleRestoreAttachment(attachment.id)}
                        >
                          <RotateCcw className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                </div>
              </div>
            )}

            {/* Новые файлы для загрузки */}
            {totalNewAttachments > 0 && (
              <div className="space-y-2">
                <div className="text-xs text-muted-foreground">Новые файлы:</div>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {selectedFiles.map((fileWithPreview) => (
                    <div key={fileWithPreview.id} className="relative group h-20">
                      {isImageFile(fileWithPreview.file) ? (
                        <Image
                          src={fileWithPreview.previewUrl}
                          alt="New file preview"
                          fill
                          sizes="(max-width: 640px) 50vw, 200px"
                          className="object-cover rounded border"
                          unoptimized
                        />
                      ) : (
                        <div className="flex flex-col items-center justify-center w-full h-full bg-muted rounded border p-1">
                          {getDocumentIcon(fileWithPreview.file.name)}
                          <span className="text-xs text-muted-foreground truncate max-w-full mt-0.5">
                            {fileWithPreview.file.name}
                          </span>
                        </div>
                      )}
                      <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={() => handleRemoveNewFile(fileWithPreview.id)}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                      {!isImageFile(fileWithPreview.file) && (
                        <div className="absolute bottom-1 left-1 right-1 bg-black/70 text-white text-xs px-1 truncate rounded">
                          {fileWithPreview.file.name}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        <DialogFooter className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            {hasChanges && "У вас есть несохраненные изменения"}
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={handleCancel} disabled={isSaving}>
              <X className="h-4 w-4 mr-1" />
              Отмена
            </Button>
            <Button onClick={handleSave} disabled={isDisabled}>
              <Save className="h-4 w-4 mr-1" />
              {isSaving ? 'Сохранение...' : 'Сохранить'}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
