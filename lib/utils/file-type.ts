import { FILE_CATEGORIES, FileCategory } from '@/lib/config/fileTypes';

/**
 * Determine the file category based on URL or MIME type
 */
export function getFileCategory(url: string, mimeType?: string): FileCategory | null {
  // Check by MIME type first if available
  if (mimeType) {
    if (mimeType.startsWith('image/')) {
      return FILE_CATEGORIES.IMAGE;
    }
    if (mimeType === 'application/pdf' || mimeType === 'text/plain' || mimeType === 'text/markdown') {
      return FILE_CATEGORIES.DOCUMENT;
    }
  }

  // Check by file extension
  const extension = url.split('.').pop()?.toLowerCase();
  if (!extension) return null;

  const imageExtensions = ['jpg', 'jpeg', 'png', 'webp', 'gif'];
  const documentExtensions = ['pdf', 'txt', 'md'];

  if (imageExtensions.includes(extension)) {
    return FILE_CATEGORIES.IMAGE;
  }
  if (documentExtensions.includes(extension)) {
    return FILE_CATEGORIES.DOCUMENT;
  }

  return null;
}

/**
 * Check if a URL points to a document file
 */
export function isDocumentUrl(url: string): boolean {
  return getFileCategory(url) === FILE_CATEGORIES.DOCUMENT;
}

/**
 * Check if a URL points to an image file
 */
export function isImageUrl(url: string): boolean {
  return getFileCategory(url) === FILE_CATEGORIES.IMAGE;
}

/**
 * Separate attachment URLs into images and documents
 */
export function separateAttachmentsByType(urls: string[]): {
  imageUrls: string[];
  documentUrls: string[];
} {
  const imageUrls: string[] = [];
  const documentUrls: string[] = [];

  for (const url of urls) {
    const category = getFileCategory(url);
    if (category === FILE_CATEGORIES.IMAGE) {
      imageUrls.push(url);
    } else if (category === FILE_CATEGORIES.DOCUMENT) {
      documentUrls.push(url);
    }
  }

  return { imageUrls, documentUrls };
}
