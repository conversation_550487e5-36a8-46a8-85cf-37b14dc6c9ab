import { describe, expect, it } from 'vitest'
import {
  getFileCategory,
  isDocumentUrl,
  isImageUrl,
  separateAttachmentsByType
} from './file-type'
import { FILE_CATEGORIES } from '@/lib/config/fileTypes'

describe('file-type utilities', () => {
  describe('getFileCategory', () => {
    it('should identify image files by MIME type', () => {
      expect(getFileCategory('test.jpg', 'image/jpeg')).toBe(FILE_CATEGORIES.IMAGE)
      expect(getFileCategory('test.png', 'image/png')).toBe(FILE_CATEGORIES.IMAGE)
      expect(getFileCategory('test.webp', 'image/webp')).toBe(FILE_CATEGORIES.IMAGE)
      expect(getFileCategory('test.gif', 'image/gif')).toBe(FILE_CATEGORIES.IMAGE)
    })

    it('should identify document files by MIME type', () => {
      expect(getFileCategory('test.pdf', 'application/pdf')).toBe(FILE_CATEGORIES.DOCUMENT)
      expect(getFileCategory('test.txt', 'text/plain')).toBe(FILE_CATEGORIES.DOCUMENT)
      expect(getFileCategory('test.md', 'text/markdown')).toBe(FILE_CATEGORIES.DOCUMENT)
    })

    it('should identify image files by extension when no MIME type', () => {
      expect(getFileCategory('https://example.com/image.jpg')).toBe(FILE_CATEGORIES.IMAGE)
      expect(getFileCategory('https://example.com/image.jpeg')).toBe(FILE_CATEGORIES.IMAGE)
      expect(getFileCategory('https://example.com/image.png')).toBe(FILE_CATEGORIES.IMAGE)
      expect(getFileCategory('https://example.com/image.webp')).toBe(FILE_CATEGORIES.IMAGE)
      expect(getFileCategory('https://example.com/image.gif')).toBe(FILE_CATEGORIES.IMAGE)
    })

    it('should identify document files by extension when no MIME type', () => {
      expect(getFileCategory('https://example.com/doc.pdf')).toBe(FILE_CATEGORIES.DOCUMENT)
      expect(getFileCategory('https://example.com/doc.txt')).toBe(FILE_CATEGORIES.DOCUMENT)
      expect(getFileCategory('https://example.com/doc.md')).toBe(FILE_CATEGORIES.DOCUMENT)
    })

    it('should handle URLs with query parameters', () => {
      expect(getFileCategory('https://example.com/image.jpg?v=123')).toBe(FILE_CATEGORIES.IMAGE)
      expect(getFileCategory('https://example.com/doc.pdf?download=true')).toBe(FILE_CATEGORIES.DOCUMENT)
    })

    it('should handle case-insensitive extensions', () => {
      expect(getFileCategory('https://example.com/image.JPG')).toBe(FILE_CATEGORIES.IMAGE)
      expect(getFileCategory('https://example.com/doc.PDF')).toBe(FILE_CATEGORIES.DOCUMENT)
      expect(getFileCategory('https://example.com/readme.MD')).toBe(FILE_CATEGORIES.DOCUMENT)
    })

    it('should return null for unknown file types', () => {
      expect(getFileCategory('https://example.com/file.xyz')).toBeNull()
      expect(getFileCategory('https://example.com/file.doc')).toBeNull()
      expect(getFileCategory('https://example.com/file')).toBeNull()
      expect(getFileCategory('no-extension')).toBeNull()
    })

    it('should return null for invalid URLs', () => {
      expect(getFileCategory('')).toBeNull()
      expect(getFileCategory('not-a-url')).toBeNull()
    })
  })

  describe('isDocumentUrl', () => {
    it('should return true for document URLs', () => {
      expect(isDocumentUrl('https://example.com/doc.pdf')).toBe(true)
      expect(isDocumentUrl('https://example.com/file.txt')).toBe(true)
      expect(isDocumentUrl('https://example.com/readme.md')).toBe(true)
    })

    it('should return false for non-document URLs', () => {
      expect(isDocumentUrl('https://example.com/image.jpg')).toBe(false)
      expect(isDocumentUrl('https://example.com/file.xyz')).toBe(false)
      expect(isDocumentUrl('')).toBe(false)
    })
  })

  describe('isImageUrl', () => {
    it('should return true for image URLs', () => {
      expect(isImageUrl('https://example.com/photo.jpg')).toBe(true)
      expect(isImageUrl('https://example.com/icon.png')).toBe(true)
      expect(isImageUrl('https://example.com/banner.webp')).toBe(true)
    })

    it('should return false for non-image URLs', () => {
      expect(isImageUrl('https://example.com/doc.pdf')).toBe(false)
      expect(isImageUrl('https://example.com/file.xyz')).toBe(false)
      expect(isImageUrl('')).toBe(false)
    })
  })

  describe('separateAttachmentsByType', () => {
    it('should separate URLs into images and documents', () => {
      const urls = [
        'https://example.com/photo1.jpg',
        'https://example.com/doc1.pdf',
        'https://example.com/photo2.png',
        'https://example.com/readme.md',
        'https://example.com/file.txt',
        'https://example.com/animation.gif'
      ]

      const result = separateAttachmentsByType(urls)

      expect(result.imageUrls).toEqual([
        'https://example.com/photo1.jpg',
        'https://example.com/photo2.png',
        'https://example.com/animation.gif'
      ])

      expect(result.documentUrls).toEqual([
        'https://example.com/doc1.pdf',
        'https://example.com/readme.md',
        'https://example.com/file.txt'
      ])
    })

    it('should handle empty array', () => {
      const result = separateAttachmentsByType([])
      expect(result.imageUrls).toEqual([])
      expect(result.documentUrls).toEqual([])
    })

    it('should ignore unknown file types', () => {
      const urls = [
        'https://example.com/photo.jpg',
        'https://example.com/unknown.xyz',
        'https://example.com/doc.pdf',
        'https://example.com/no-extension'
      ]

      const result = separateAttachmentsByType(urls)

      expect(result.imageUrls).toEqual(['https://example.com/photo.jpg'])
      expect(result.documentUrls).toEqual(['https://example.com/doc.pdf'])
    })

    it('should handle Supabase storage URLs', () => {
      const urls = [
        'https://xyz.supabase.co/storage/v1/object/public/attachments/images/user123/uuid_photo.jpg',
        'https://xyz.supabase.co/storage/v1/object/public/attachments/documents/user123/uuid_document.pdf',
        'https://xyz.supabase.co/storage/v1/object/public/attachments/documents/user123/uuid_notes.txt'
      ]

      const result = separateAttachmentsByType(urls)

      expect(result.imageUrls).toHaveLength(1)
      expect(result.documentUrls).toHaveLength(2)
    })
  })
})
