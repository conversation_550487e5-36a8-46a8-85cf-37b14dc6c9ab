import { createClient } from '@/lib/supabase/server'
import { DatabaseError } from './types'

export async function getAllTagsByUserId(userId: string): Promise<string[]> {
  try {
    const supabase = await createClient()

    // First approach: Get all note_tags for user's notes
    const { data: noteTags, error: noteTagsError } = await supabase
      .from('note_tags')
      .select(`
        tags (
          id,
          name
        ),
        notes!inner (
          user_id
        )
      `)
      .eq('notes.user_id', userId)

    if (noteTagsError) {
      console.error('[getAllTagsByUserId] Database error fetching note_tags:', noteTagsError)
      throw new DatabaseError('Could not fetch tags from the database', noteTagsError)
    }

    // Extract unique tag names
    const tagNames = new Set<string>()
    noteTags?.forEach(noteTag => {
      // tags should be a single object, not an array
      // Use proper type guard instead of 'as any'
      if (noteTag.tags &&
          typeof noteTag.tags === 'object' &&
          'name' in noteTag.tags &&
          typeof noteTag.tags.name === 'string') {
        tagNames.add(noteTag.tags.name)
      }
    })

    const uniqueTags = Array.from(tagNames).sort()

    return uniqueTags
  } catch (error) {
    if (error instanceof DatabaseError) {
      throw error
    }
    console.error('Unexpected error fetching tags:', error)
    throw new DatabaseError('Unexpected error while fetching tags', error)
  }
}

export async function createOrGetTag(tagName: string): Promise<string> {
  try {
    const supabase = await createClient()

    const { data: existingTag, error: selectError } = await supabase
      .from('tags')
      .select('id')
      .eq('name', tagName)
      .single()

    if (!selectError && existingTag) {
      return existingTag.id
    }

    const { data: newTag, error: insertError } = await supabase
      .from('tags')
      .insert({ name: tagName })
      .select('id')
      .single()

    if (insertError || !newTag) {
      console.error('Database error creating tag:', insertError)
      throw new DatabaseError('Could not create tag in the database', insertError)
    }

    return newTag.id
  } catch (error) {
    if (error instanceof DatabaseError) {
      throw error
    }
    console.error('Unexpected error creating/getting tag:', error)
    throw new DatabaseError('Unexpected error while creating/getting tag', error)
  }
}

export async function addTagsToNote(noteId: string, tags: string[]): Promise<void> {
  try {
    const supabase = await createClient()

    for (const tagName of tags) {
      const tagId = await createOrGetTag(tagName)

      const { error: upsertError } = await supabase
        .from('note_tags')
        .upsert({ note_id: noteId, tag_id: tagId })

      if (upsertError) {
        console.error('Database error linking tag to note:', upsertError)
        throw new DatabaseError('Could not link tag to note in the database', upsertError)
      }
    }
  } catch (error) {
    if (error instanceof DatabaseError) {
      throw error
    }
    console.error('Unexpected error adding tags to note:', error)
    throw new DatabaseError('Unexpected error while adding tags to note', error)
  }
}
