import { STORAGE_CONFIG, STORAGE_ERRORS, type AllowedMimeType, type AllowedExtension } from './config'
import { FILE_TYPE_CONFIG, type FileCategory } from '@/lib/config/fileTypes'

export interface FileValidationResult {
  isValid: boolean
  error?: string
}

export interface FileInfo {
  name: string
  size: number
  type: string
}

/**
 * Validate file for upload to note attachments
 * @deprecated Use validateFileWithCategory instead
 */
export function validateFile(file: FileInfo): FileValidationResult {
  // Check file size
  if (file.size > STORAGE_CONFIG.MAX_FILE_SIZE) {
    return {
      isValid: false,
      error: STORAGE_ERRORS.FILE_TOO_LARGE(formatFileSize(STORAGE_CONFIG.MAX_FILE_SIZE))
    }
  }

  // Check MIME type
  if (!STORAGE_CONFIG.ALLOWED_MIME_TYPES.includes(file.type as AllowedMimeType)) {
    return {
      isValid: false,
      error: STORAGE_ERRORS.INVALID_FILE_TYPE
    }
  }

  // Check file extension
  const extension = getFileExtension(file.name).toLowerCase()
  if (!STORAGE_CONFIG.ALLOWED_EXTENSIONS.includes(extension as AllowedExtension)) {
    return {
      isValid: false,
      error: STORAGE_ERRORS.INVALID_FILE_EXTENSION
    }
  }

  return { isValid: true }
}

/**
 * Validate file for upload based on file category
 */
export function validateFileWithCategory(file: FileInfo, category: FileCategory): FileValidationResult {
  const config = FILE_TYPE_CONFIG[category]

  // Check file size
  if (file.size > config.maxSize) {
    return {
      isValid: false,
      error: STORAGE_ERRORS.FILE_TOO_LARGE(formatFileSize(config.maxSize))
    }
  }

  // Check MIME type
  if (!config.allowedMimeTypes.includes(file.type)) {
    return {
      isValid: false,
      error: STORAGE_ERRORS.INVALID_FILE_TYPE
    }
  }

  return { isValid: true }
}



/**
 * Get file extension from filename
 */
export function getFileExtension(filename: string): string {
  const lastDotIndex = filename.lastIndexOf('.')
  return lastDotIndex !== -1 ? filename.substring(lastDotIndex) : ''
}

/**
 * Format file size in human readable format
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * Check if file is an image based on MIME type
 */
export function isImageFile(mimeType: string): boolean {
  return STORAGE_CONFIG.ALLOWED_MIME_TYPES.includes(mimeType as AllowedMimeType)
}
