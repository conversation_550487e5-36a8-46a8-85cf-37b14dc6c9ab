import { createClient } from '@/lib/supabase/server'
import { STORAGE_CONFIG, STORAGE_ERRORS } from './config'
import { validateFileWithCategory, type FileInfo } from './validation'
import { FILE_CATEGORIES, type FileCategory } from '@/lib/config/fileTypes'

export interface UploadUrlResponse {
  uploadUrl: string
  filePath: string
  publicUrl: string
}

export interface UploadError {
  error: string
}

/**
 * Generate unique file name for storage with category folder
 */
function generateFileName(userId: string, originalName: string, category: FileCategory): string {
  const timestamp = Date.now()
  const randomSuffix = Math.random().toString(36).substring(2, 8)
  const extension = getFileExtension(originalName)

  // Add category folder prefix
  const categoryFolder = category === FILE_CATEGORIES.IMAGE ? 'attachments/images' : 'attachments/documents'

  return `${categoryFolder}/${userId}/${timestamp}_${randomSuffix}${extension}`
}

/**
 * Get file extension from filename
 */
function getFileExtension(filename: string): string {
  const lastDotIndex = filename.lastIndexOf('.')
  return lastDotIndex !== -1 ? filename.substring(lastDotIndex) : ''
}

/**
 * Generate signed URL for file upload with category support
 */
export async function generateUploadUrl(
  userId: string,
  fileInfo: FileInfo,
  category: FileCategory = FILE_CATEGORIES.IMAGE
): Promise<UploadUrlResponse | UploadError> {
  try {
    // Validate file with category
    const validation = validateFileWithCategory(fileInfo, category)
    if (!validation.isValid) {
      return { error: validation.error! }
    }

    const supabase = await createClient()

    // Generate unique file path with category folder
    const filePath = generateFileName(userId, fileInfo.name, category)

    // Create signed URL for upload
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from(STORAGE_CONFIG.BUCKET_NAME)
      .createSignedUploadUrl(filePath, {
        upsert: false
      })

    if (uploadError) {
      console.error('Error creating signed upload URL:', uploadError)
      return { error: STORAGE_ERRORS.UPLOAD_URL_FAILED }
    }

    // Get public URL for the file (bucket is now public)
    const { data: publicUrlData } = supabase.storage
      .from(STORAGE_CONFIG.BUCKET_NAME)
      .getPublicUrl(filePath)

    return {
      uploadUrl: uploadData.signedUrl,
      filePath,
      publicUrl: publicUrlData.publicUrl
    }
  } catch (error) {
    console.error('Unexpected error generating upload URL:', error)
    return { error: 'Произошла неожиданная ошибка при подготовке загрузки' }
  }
}

/**
 * Delete file from storage
 */
export async function deleteFile(filePath: string): Promise<boolean> {
  try {
    const supabase = await createClient()

    const { error } = await supabase.storage
      .from(STORAGE_CONFIG.BUCKET_NAME)
      .remove([filePath])

    if (error) {
      console.error('Error deleting file:', error)
      return false
    }

    return true
  } catch (error) {
    console.error('Unexpected error deleting file:', error)
    return false
  }
}



/**
 * Extract file path from public URL
 */
export function extractFilePathFromUrl(publicUrl: string): string | null {
  try {
    const url = new URL(publicUrl)
    const pathParts = url.pathname.split('/')
    const bucketIndex = pathParts.findIndex(part => part === STORAGE_CONFIG.BUCKET_NAME)

    if (bucketIndex === -1 || bucketIndex === pathParts.length - 1) {
      return null
    }

    return pathParts.slice(bucketIndex + 1).join('/')
  } catch {
    return null
  }
}
