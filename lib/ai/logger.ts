type LogLevel = 'request' | 'response' | 'result' | 'error' | 'fallback'

interface LogContext {
  operation: 'analyze' | 'search'
  model?: string
  temperature?: number
  [key: string]: unknown
}

class AILogger {
  private isEnabled = process.env.NODE_ENV === 'development'

  private formatHeader(level: LogLevel, operation: string): string {
    const headers = {
      request: `=== AI ${operation.toUpperCase()} REQUEST ===`,
      response: `=== AI ${operation.toUpperCase()} RESPONSE ===`,
      result: `=== AI ${operation.toUpperCase()} FINAL RESULT ===`,
      error: `=== AI ${operation.toUpperCase()} ERROR ===`,
      fallback: `=== AI ${operation.toUpperCase()} FALLBACK ===`
    }
    return headers[level]
  }

  private formatFooter(level: LogLevel): string {
    const length = this.formatHeader(level, 'test').length
    return '='.repeat(length)
  }

  logRequest(context: LogContext, prompt: string) {
    if (!this.isEnabled) return

    console.log(this.formatHeader('request', context.operation))
    if (context.model) console.log('Model:', context.model)
    if (context.temperature !== undefined) console.log('Temperature:', context.temperature)

    Object.entries(context).forEach(([key, value]) => {
      if (!['operation', 'model', 'temperature'].includes(key)) {
        console.log(`${key}:`, value)
      }
    })

    console.log('Prompt sent to AI:')
    console.log(prompt)
    console.log(this.formatFooter('request'))
  }

  logResponse(context: LogContext, rawResponse: string | object) {
    if (!this.isEnabled) return

    console.log(this.formatHeader('response', context.operation))
    console.log('Raw response from AI:')
    console.log(typeof rawResponse === 'string' ? rawResponse : JSON.stringify(rawResponse, null, 2))
    console.log(this.formatFooter('response'))
  }

  logResult(context: LogContext, result: unknown) {
    if (!this.isEnabled) return

    console.log(this.formatHeader('result', context.operation))
    console.log('Parsed result:', result)
    console.log(this.formatFooter('result'))
  }

  logError(context: LogContext, error: unknown, details?: Record<string, unknown>) {
    if (!this.isEnabled) return

    console.error(this.formatHeader('error', context.operation))
    console.error('Error:', error)
    if (details) {
      Object.entries(details).forEach(([key, value]) => {
        console.error(`${key}:`, value)
      })
    }
    console.error(this.formatFooter('error'))
  }

  logFallback(context: LogContext, result: unknown, reason?: string) {
    if (!this.isEnabled) return

    console.log(this.formatHeader('fallback', context.operation))
    if (reason) console.log('Reason:', reason)
    console.log('Fallback result:', result)
    console.log(this.formatFooter('fallback'))
  }
}

export const aiLogger = new AILogger()
