import { generateObject } from 'ai'
import { getModel, MULTIMODAL_MODEL } from './openrouter'
import { analyzeResultSchema } from './schemas'
import { AI_CONSTANTS } from './utils'

/**
 * Test if AI SDK can process document URLs directly
 *
 * Results:
 * - The AI SDK does NOT support direct document URL analysis
 * - FilePart requires 'data' and 'mimeType', not URLs
 * - Only images can be passed as URLs
 * - We need to use Firecrawl to extract text from documents first
 */
export async function testDocumentAnalysis() {
  console.log('Testing document analysis capabilities...\n')
  console.log('Note: Based on TypeScript errors, AI SDK does not support direct document URLs.')
  console.log('FilePart requires data and mimeType, not URLs.')
  console.log('Only images can be passed as URLs to the AI SDK.')

  // Test if PDF can be treated as image (unlikely to work but worth testing)
  const testUrl = 'https://example.com/test.pdf'

  try {
    const messages = [{
      role: 'user' as const,
      content: [
        { type: 'text' as const, text: 'Analyze this document and extract key information.' },
        { type: 'image' as const, image: testUrl }
      ]
    }]

    console.log('\nTesting PDF as image...')
    const { object } = await generateObject({
      model: getModel(MULTIMODAL_MODEL),
      messages,
      temperature: AI_CONSTANTS.ANALYZE_TEMPERATURE,
      schema: analyzeResultSchema,
      maxTokens: AI_CONSTANTS.MAX_TOKENS
    })

    console.log('✅ Success! PDF can be treated as image')
    console.log('Response:', JSON.stringify(object, null, 2))

  } catch (error) {
    console.log('❌ Failed: PDF cannot be treated as image')
    console.log('Error:', error instanceof Error ? error.message : 'Unknown error')
    console.log('\n⚠️  Conclusion: We need to use Firecrawl to extract text from documents')
  }
}

// Run the test
if (require.main === module) {
  testDocumentAnalysis().catch(console.error)
}
