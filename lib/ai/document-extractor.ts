import { scrapeUrl } from '@/lib/firecrawl/scraper';

const DOCUMENT_EXTRACTION_PROMPT = `
[Содержимое документа]
---
{documentContent}
---
[Конец документа]`;

/**
 * Extract text content from a document URL using Firecrawl
 */
export async function extractTextFromDocument(documentUrl: string): Promise<string | null> {
  try {
    console.log(`[Document Extraction] Extracting text from document: ${documentUrl}`);

    // Use Firecrawl to extract text from the document
    const result = await scrapeUrl(documentUrl);

    if (!result.success || !result.content) {
      console.error(`[Document Extraction] Failed to extract from ${documentUrl}:`, result.error || 'No content');
      return null;
    }

    // Format the extracted content for AI analysis
    const formattedContent = DOCUMENT_EXTRACTION_PROMPT.replace('{documentContent}', result.content);

    console.log(`[Document Extraction] Successfully extracted ${result.content.length} characters from ${documentUrl}`);

    return formattedContent;
  } catch (error) {
    console.error(`[Document Extraction] Error extracting from ${documentUrl}:`, error);
    return null;
  }
}

/**
 * Extract text from multiple documents
 */
export async function extractTextFromDocuments(documentUrls: string[]): Promise<Map<string, string>> {
  const extractedTexts = new Map<string, string>();

  for (const url of documentUrls) {
    const text = await extractTextFromDocument(url);
    if (text) {
      extractedTexts.set(url, text);
    }
  }

  return extractedTexts;
}

/**
 * Combine note content with extracted document texts
 */
export function combineContentWithDocuments(
  noteContent: string,
  extractedDocuments: Map<string, string>
): string {
  if (extractedDocuments.size === 0) {
    return noteContent;
  }

  let combinedContent = noteContent;

  // Add extracted document content
  for (const [, documentText] of extractedDocuments) {
    combinedContent += `\n\n${documentText}`;
  }

  return combinedContent;
}
