/**
 * Centralized configuration for attachment functionality
 * This file contains all constants related to file attachments to avoid duplication
 */

// File attachment limits
export const ATTACHMENT_LIMITS = {
  MAX_PER_NOTE: 3,
  MIN_PER_REQUEST: 1
} as const

// Error messages for attachments
export const ATTACHMENT_ERROR_MESSAGES = {
  // Database errors
  FETCH_FAILED: 'Could not fetch attachments from the database',
  CREATE_FAILED: 'Could not create attachments in the database',
  DELETE_FAILED: 'Could not delete attachment from the database',
  COUNT_CHECK_FAILED: 'Could not check current attachment count',

  // Validation errors
  MAX_LIMIT_EXCEEDED: () =>
    `Maximum ${ATTACHMENT_LIMITS.MAX_PER_NOTE} attachments allowed per note`,
  LIMIT_WOULD_EXCEED: (adding: number, current: number) =>
    `Cannot add ${adding} attachments. Note already has ${current} attachments. Maximum ${ATTACHMENT_LIMITS.MAX_PER_NOTE} allowed.`,

  // Access control errors
  ACCESS_DENIED: 'Attachment not found or access denied',
  USER_ACCESS_DENIED: 'Access denied: attachment belongs to another user',

  // File validation errors
  FILE_NAME_REQUIRED: 'Имя файла обязательно',
  FILE_SIZE_POSITIVE: 'Размер файла должен быть положительным',
  FILE_TYPE_REQUIRED: 'Тип файла обязателен',
  MIN_FILES_REQUIRED: `Минимум ${ATTACHMENT_LIMITS.MIN_PER_REQUEST} файл обязателен`,
  MAX_FILES_EXCEEDED: `Максимум ${ATTACHMENT_LIMITS.MAX_PER_NOTE} файла разрешено`,

  // Upload errors
  UPLOAD_FAILED: 'Ошибка загрузки файлов',
  UPLOAD_PARTIAL_FAILURE: 'Загрузка файлов не удалась',

  // AI Analysis errors
  ANALYSIS_SAVE_FAILED: 'Failed to save attachment analysis error',
  ANALYSIS_CLEAR_FAILED: 'Failed to clear attachment analysis errors'
} as const

// UI text constants
export const ATTACHMENT_UI_TEXT = {
  PLACEHOLDER: "Напишите заметку...",
  BUTTON_TEXT: "Изображение",
  BUTTON_TEXT_WITH_LIMIT: (canAddMore: boolean) =>
    `Изображение ${!canAddMore ? `(макс. ${ATTACHMENT_LIMITS.MAX_PER_NOTE})` : ''}`,

  // File counter
  FILES_COUNTER: (current: number) =>
    `Прикрепленные изображения (${current}/${ATTACHMENT_LIMITS.MAX_PER_NOTE}):`,

  // Status messages
  LIMIT_REACHED: 'Достигнут лимит',
  UPLOADING: 'Загрузка...',
  SAVING: 'Сохранение...',
  SUBMIT: 'Отправить',

  // Error messages for UI
  TOO_MANY_FILES_NO_SLOTS: `Достигнут лимит в ${ATTACHMENT_LIMITS.MAX_PER_NOTE} изображения. Удалите существующие, чтобы добавить новые.`,
  TOO_MANY_FILES_WITH_SLOTS: (remaining: number, attempted: number) => {
    const pluralForm = remaining === 1 ? 'е' : 'й'
    return `Можно добавить еще ${remaining} изображени${pluralForm}. Выбрано: ${attempted}`
  },

  // Generic error
  FILE_SELECTION_ERROR: 'Ошибка при выборе файла'
} as const

// File upload configuration
export const UPLOAD_CONFIG = {
  ACCEPTED_TYPES: 'image/*',
  MULTIPLE: true,
  MAX_CONCURRENT_UPLOADS: ATTACHMENT_LIMITS.MAX_PER_NOTE
} as const

// API endpoint paths
export const ATTACHMENT_API_PATHS = {
  UPLOAD_URL: '/api/notes/upload-url',
  CREATE_NOTE: '/api/notes'
} as const
