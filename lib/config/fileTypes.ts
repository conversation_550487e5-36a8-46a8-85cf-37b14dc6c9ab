export const FILE_CATEGORIES = {
  IMAGE: 'image',
  DOCUMENT: 'document',
} as const;

export type FileCategory = typeof FILE_CATEGORIES[keyof typeof FILE_CATEGORIES];

export const FILE_TYPE_CONFIG = {
  [FILE_CATEGORIES.IMAGE]: {
    allowedMimeTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'],
    maxSize: 10 * 1024 * 1024, // 10 MB
    maxCount: 3,
    accept: 'image/*',
  },
  [FILE_CATEGORIES.DOCUMENT]: {
    allowedMimeTypes: ['application/pdf', 'text/plain', 'text/markdown'],
    maxSize: 10 * 1024 * 1024, // 10 MB
    maxCount: 3,
    accept: '.pdf,.txt,.md',
  },
};

export const TOTAL_ATTACHMENT_LIMIT = 3;
