-- Fix ambiguous column reference in update_note_with_tags function
-- The issue is with variable naming conflicts in the function

CREATE OR REPLACE FUNCTION update_note_with_tags(
  p_note_id UUID,
  p_user_id UUID,
  p_summary TEXT,
  p_tags TEXT[]
) RETURNS JSON AS $$
DECLARE
  tag_record RECORD;
  final_tag_ids UUID[];
  new_tag_names TEXT[];
  result JSON;
BEGIN
  -- Check that the note belongs to the user
  IF NOT EXISTS (
    SELECT 1 FROM notes
    WHERE id = p_note_id AND user_id = p_user_id
  ) THEN
    RAISE EXCEPTION 'Note not found or access denied';
  END IF;

  -- Update note summary
  UPDATE notes
  SET summary_ai = p_summary, updated_at = NOW()
  WHERE id = p_note_id AND user_id = p_user_id;

  -- Delete all existing tag associations with the note
  DELETE FROM note_tags WHERE note_id = p_note_id;

  -- If no tags to add, return result
  IF array_length(p_tags, 1) IS NULL OR array_length(p_tags, 1) = 0 THEN
    SELECT json_build_object(
      'success', true,
      'tags', '[]'::json,
      'summary', p_summary
    ) INTO result;
    RETURN result;
  END IF;

  -- Determine which tags need to be created
  SELECT array_agg(tag_name)
  INTO new_tag_names
  FROM unnest(p_tags) AS tag_name
  WHERE tag_name NOT IN (
    SELECT name FROM tags WHERE name = ANY(p_tags)
  );

  -- Create new tags in one query, if any exist
  IF array_length(new_tag_names, 1) > 0 THEN
    INSERT INTO tags (name)
    SELECT unnest(new_tag_names)
    ON CONFLICT (name) DO NOTHING;
  END IF;

  -- Get all tag IDs in one query
  SELECT array_agg(tags.id)
  INTO final_tag_ids
  FROM tags
  WHERE tags.name = ANY(p_tags);

  -- Create associations between note and tags in one query
  INSERT INTO note_tags (note_id, tag_id)
  SELECT p_note_id, unnest(final_tag_ids)
  ON CONFLICT (note_id, tag_id) DO NOTHING;

  -- Format result
  SELECT json_build_object(
    'success', true,
    'tags', json_agg(tags.name ORDER BY tags.name),
    'summary', p_summary
  )
  INTO result
  FROM tags
  WHERE tags.id = ANY(final_tag_ids);

  RETURN result;

EXCEPTION
  WHEN OTHERS THEN
    -- On error, rollback transaction and return error
    RAISE EXCEPTION 'Failed to update note with tags: %', SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execution rights to authenticated users
GRANT EXECUTE ON FUNCTION update_note_with_tags(UUID, UUID, TEXT, TEXT[]) TO authenticated;
