-- Fix trigger to prevent updated_at from being bumped when only AI analysis status changes
-- This prevents newly created notes from showing "modified" status immediately

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    -- Only update updated_at if fields other than ai_analysis_status or summary_ai have changed
    -- This prevents AI background processes from marking notes as "modified"
    IF (
        -- Check if any user-facing fields have changed
        OLD.content IS DISTINCT FROM NEW.content OR
        OLD.content_type IS DISTINCT FROM NEW.content_type
        -- Don't include ai_analysis_status and summary_ai in the check
        -- as these are updated by background AI processes
        -- Additional fields can be added here with OR if needed in the future
    ) THEN
        NEW.updated_at = TIMEZONE('utc', NOW());
    ELSE
        -- Keep the original updated_at if only AI fields changed
        NEW.updated_at = OLD.updated_at;
    END IF;

    RETURN NEW;
END;
$$ language 'plpgsql';

-- The trigger is already created, so we don't need to recreate it
-- CREATE TRIGGER update_notes_updated_at
--     BEFORE UPDATE ON notes
--     FOR EACH ROW
--     EXECUTE FUNCTION update_updated_at_column();

-- Add comment for documentation
COMMENT ON FUNCTION update_updated_at_column() IS
'Updates updated_at timestamp only when user-facing fields change, not when AI analysis fields (ai_analysis_status, summary_ai) are updated by background processes.';
